<?php
// 处理主题设置
$theme = isset($_COOKIE['theme_mode']) ? $_COOKIE['theme_mode'] : 'light';
$color_scheme = isset($_COOKIE['color_scheme']) ? $_COOKIE['color_scheme'] : '#206bc4';
$font_family = isset($_COOKIE['font_family']) ? $_COOKIE['font_family'] : 'sans-serif';
$theme_base = isset($_COOKIE['theme_base']) ? $_COOKIE['theme_base'] : 'gray';
$corner_radius = isset($_COOKIE['corner_radius']) ? $_COOKIE['corner_radius'] : '1';

// 处理旧版主题切换参数兼容
if (isset($_GET['theme'])) {
    setcookie('theme_mode', $_GET['theme'], time() + 30 * 24 * 60 * 60, '/');
    $theme = $_GET['theme'];
    // 重定向到当前页面，移除theme参数
    $redirect_url = strtok($_SERVER['REQUEST_URI'], '?');
    if (isset($_GET['page'])) {
        $redirect_url .= '?page=' . $_GET['page'];
    }
    header('Location: ' . $redirect_url);
    exit;
}
?>
<!DOCTYPE html>
<html lang="zh-CN" data-bs-theme="<?php echo $theme; ?>" style="--tblr-primary: <?php echo $color_scheme; ?>; --tblr-border-radius-scale: <?php echo $corner_radius; ?>; --tblr-font-sans-serif: <?php echo $font_family; ?>, -apple-system, BlinkMacSystemFont, San Francisco, Segoe UI, Roboto, Helvetica Neue, sans-serif;">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>管理后台</title>
    <!-- Tabler CSS 最新版本 -->
    <link rel="stylesheet" href="https://cdn.jsdmirror.com/npm/@tabler/core@1.4.0/dist/css/tabler.min.css">
    <link rel="stylesheet" href="https://cdn.jsdmirror.com/npm/@tabler/icons-webfont@latest/tabler-icons.min.css">
    <!-- 自定义样式 -->
    <style>
        .navbar-brand-image {
            width: 110px;
            height: auto;
        }
    </style>
</head>
<body>
    <div class="page">
        <!-- 顶部导航栏 -->
        <header class="navbar navbar-expand-md navbar-light d-print-none">
            <div class="container-xl">
                <button class="navbar-toggler" type="button" data-bs-toggle="collapse" data-bs-target="#navbar-menu" aria-controls="navbar-menu" aria-expanded="false" aria-label="Toggle navigation">
                    <span class="navbar-toggler-icon"></span>
                </button>
                <h1 class="navbar-brand navbar-brand-autodark d-none-navbar-horizontal pe-0 pe-md-3">
                    <a href="index.php">
                        <img src="https://25y.wujiyan.cc/upload/logo.svg" alt="Logo" class="navbar-brand-image">
                    </a>
                </h1>
                <div class="navbar-nav flex-row order-md-last">
                    <div class="d-flex me-2">
                        <a href="#" class="nav-link px-0" title="主题设置" data-bs-toggle="modal" data-bs-target="#themeSettingsModal">
                            <i class="ti ti-palette"></i>
                        </a>
                    </div>
                    <div class="nav-item dropdown">
                        <a href="#" class="nav-link d-flex lh-1 text-reset p-0" data-bs-toggle="dropdown" aria-label="Open user menu">
                            <span class="avatar avatar-sm" style="background-image: url(https://25y.wujiyan.cc/themes/clientarea/user/static/images/avatar.png)"></span>
                            <div class="d-none d-xl-block ps-2">
                                <div>管理员</div>
                                <div class="mt-1 small text-muted">超级管理员</div>
                            </div>
                        </a>
                        <div class="dropdown-menu dropdown-menu-end dropdown-menu-arrow">
                            <a href="index.php?page=profile" class="dropdown-item">个人资料</a>
                            <a href="index.php?page=settings" class="dropdown-item">设置</a>
                            <div class="dropdown-divider"></div>
                            <a href="index.php?page=logout" class="dropdown-item">退出登录</a>
                        </div>
                    </div>
                </div>
            </div>
        </header>
        <!-- 水平导航菜单 -->
        <header class="navbar-expand-md">
            <div class="collapse navbar-collapse" id="navbar-menu">
                <div class="navbar navbar-light">
                    <div class="container-xl">
                        <ul class="navbar-nav">
                            <li class="nav-item">
                                <a class="nav-link" href="index.php?page=dashboard">
                                    <span class="nav-link-icon d-md-none d-lg-inline-block">
                                        <i class="ti ti-home"></i>
                                    </span>
                                    <span class="nav-link-title">仪表盘</span>
                                </a>
                            </li>
                            <li class="nav-item dropdown">
                                <a class="nav-link dropdown-toggle" href="#navbar-base" data-bs-toggle="dropdown" data-bs-auto-close="false" role="button" aria-expanded="false">
                                    <span class="nav-link-icon d-md-none d-lg-inline-block">
                                        <i class="ti ti-package"></i>
                                    </span>
                                    <span class="nav-link-title">系统管理</span>
                                </a>
                                <div class="dropdown-menu">
                                    <a class="dropdown-item" href="index.php?page=users">
                                        用户管理
                                    </a>
                                    <a class="dropdown-item" href="index.php?page=settings">
                                        系统设置
                                    </a>
                                    <a class="dropdown-item" href="index.php?page=update">
                                        模板更新
                                    </a>
                                </div>
                            </li>
                            <li class="nav-item">
                                <a class="nav-link" href="index.php?page=logs">
                                    <span class="nav-link-icon d-md-none d-lg-inline-block">
                                        <i class="ti ti-file-text"></i>
                                    </span>
                                    <span class="nav-link-title">系统日志</span>
                                </a>
                            </li>
                        </ul>
                        <div class="my-2 my-md-0 flex-grow-1 flex-md-grow-0 order-first order-md-last">
                            <form action="" method="get">
                                <div class="input-icon">
                                    <span class="input-icon-addon">
                                        <i class="ti ti-search"></i>
                                    </span>
                                    <input type="text" class="form-control" placeholder="搜索...">
                                </div>
                            </form>
                        </div>
                    </div>
                </div>
            </div>
        </header>
        <!-- 主题设置模态框 -->
        <div class="modal modal-blur fade" id="themeSettingsModal" tabindex="-1" role="dialog" aria-hidden="true">
            <div class="modal-dialog modal-dialog-centered" role="document">
                <div class="modal-content">
                    <div class="modal-header">
                        <h5 class="modal-title">主题设置</h5>
                        <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
                    </div>
                    <div class="modal-body">
                        <!-- 颜色模式 -->
                        <div class="mb-3">
                            <label class="form-label">颜色模式</label>
                            <div class="form-text text-muted mb-2">选择适合您应用的颜色模式。</div>
                            <div>
                                <label class="form-check form-check-inline">
                                    <input class="form-check-input" type="radio" name="color-mode" value="light" checked>
                                    <span class="form-check-label">明亮</span>
                                </label>
                                <label class="form-check form-check-inline">
                                    <input class="form-check-input" type="radio" name="color-mode" value="dark">
                                    <span class="form-check-label">暗黑</span>
                                </label>
                            </div>
                        </div>
                        <!-- 颜色方案 -->
                        <div class="mb-3">
                            <label class="form-label">颜色方案</label>
                            <div class="form-text text-muted mb-2">为您的应用选择完美的颜色方案。</div>
                            <div class="row g-2">
                                <div class="col-auto">
                                    <label class="form-colorinput">
                                        <input name="color-scheme" type="radio" value="#206bc4" class="form-colorinput-input" checked>
                                        <span class="form-colorinput-color bg-blue"></span>
                                    </label>
                                </div>
                                <div class="col-auto">
                                    <label class="form-colorinput">
                                        <input name="color-scheme" type="radio" value="#4299e1" class="form-colorinput-input">
                                        <span class="form-colorinput-color bg-azure"></span>
                                    </label>
                                </div>
                                <div class="col-auto">
                                    <label class="form-colorinput">
                                        <input name="color-scheme" type="radio" value="#4263eb" class="form-colorinput-input">
                                        <span class="form-colorinput-color bg-indigo"></span>
                                    </label>
                                </div>
                                <div class="col-auto">
                                    <label class="form-colorinput">
                                        <input name="color-scheme" type="radio" value="#ae3ec9" class="form-colorinput-input">
                                        <span class="form-colorinput-color bg-purple"></span>
                                    </label>
                                </div>
                                <div class="col-auto">
                                    <label class="form-colorinput">
                                        <input name="color-scheme" type="radio" value="#d6336c" class="form-colorinput-input">
                                        <span class="form-colorinput-color bg-pink"></span>
                                    </label>
                                </div>
                                <div class="col-auto">
                                    <label class="form-colorinput">
                                        <input name="color-scheme" type="radio" value="#e53e3e" class="form-colorinput-input">
                                        <span class="form-colorinput-color bg-red"></span>
                                    </label>
                                </div>
                                <div class="col-auto">
                                    <label class="form-colorinput">
                                        <input name="color-scheme" type="radio" value="#dd6b20" class="form-colorinput-input">
                                        <span class="form-colorinput-color bg-orange"></span>
                                    </label>
                                </div>
                                <div class="col-auto">
                                    <label class="form-colorinput">
                                        <input name="color-scheme" type="radio" value="#f59f00" class="form-colorinput-input">
                                        <span class="form-colorinput-color bg-yellow"></span>
                                    </label>
                                </div>
                                <div class="col-auto">
                                    <label class="form-colorinput">
                                        <input name="color-scheme" type="radio" value="#5eba00" class="form-colorinput-input">
                                        <span class="form-colorinput-color bg-lime"></span>
                                    </label>
                                </div>
                                <div class="col-auto">
                                    <label class="form-colorinput">
                                        <input name="color-scheme" type="radio" value="#2fb344" class="form-colorinput-input">
                                        <span class="form-colorinput-color bg-green"></span>
                                    </label>
                                </div>
                                <div class="col-auto">
                                    <label class="form-colorinput">
                                        <input name="color-scheme" type="radio" value="#0ca678" class="form-colorinput-input">
                                        <span class="form-colorinput-color bg-teal"></span>
                                    </label>
                                </div>
                                <div class="col-auto">
                                    <label class="form-colorinput">
                                        <input name="color-scheme" type="radio" value="#0dcaf0" class="form-colorinput-input">
                                        <span class="form-colorinput-color bg-cyan"></span>
                                    </label>
                                </div>
                            </div>
                        </div>
                        <!-- 字体系列 -->
                        <div class="mb-3">
                            <label class="form-label">字体系列</label>
                            <div class="form-text text-muted mb-2">选择适合您应用的字体系列。</div>
                            <div>
                                <label class="form-check form-check-inline">
                                    <input class="form-check-input" type="radio" name="font-family" value="sans-serif" checked>
                                    <span class="form-check-label">无衬线字体</span>
                                </label>
                                <label class="form-check form-check-inline">
                                    <input class="form-check-input" type="radio" name="font-family" value="serif">
                                    <span class="form-check-label">衬线字体</span>
                                </label>
                                <label class="form-check form-check-inline">
                                    <input class="form-check-input" type="radio" name="font-family" value="monospace">
                                    <span class="form-check-label">等宽字体</span>
                                </label>
                                <label class="form-check form-check-inline">
                                    <input class="form-check-input" type="radio" name="font-family" value="comic">
                                    <span class="form-check-label">漫画字体</span>
                                </label>
                            </div>
                        </div>
                        <!-- 主题基础 -->
                        <div class="mb-3">
                            <label class="form-label">主题基础</label>
                            <div class="form-text text-muted mb-2">选择适合您应用的灰度色调。</div>
                            <div>
                                <label class="form-check form-check-inline">
                                    <input class="form-check-input" type="radio" name="theme-base" value="slate">
                                    <span class="form-check-label">石板灰</span>
                                </label>
                                <label class="form-check form-check-inline">
                                    <input class="form-check-input" type="radio" name="theme-base" value="gray" checked>
                                    <span class="form-check-label">灰色</span>
                                </label>
                                <label class="form-check form-check-inline">
                                    <input class="form-check-input" type="radio" name="theme-base" value="zinc">
                                    <span class="form-check-label">锌灰</span>
                                </label>
                                <label class="form-check form-check-inline">
                                    <input class="form-check-input" type="radio" name="theme-base" value="neutral">
                                    <span class="form-check-label">中性灰</span>
                                </label>
                                <label class="form-check form-check-inline">
                                    <input class="form-check-input" type="radio" name="theme-base" value="stone">
                                    <span class="form-check-label">石灰</span>
                                </label>
                            </div>
                        </div>
                        <!-- 圆角半径 -->
                        <div class="mb-3">
                            <label class="form-label">圆角半径</label>
                            <div class="form-text text-muted mb-2">选择适合您应用的边框圆角系数。</div>
                            <div>
                                <label class="form-check form-check-inline">
                                    <input class="form-check-input" type="radio" name="corner-radius" value="0">
                                    <span class="form-check-label">0</span>
                                </label>
                                <label class="form-check form-check-inline">
                                    <input class="form-check-input" type="radio" name="corner-radius" value="0.5">
                                    <span class="form-check-label">0.5</span>
                                </label>
                                <label class="form-check form-check-inline">
                                    <input class="form-check-input" type="radio" name="corner-radius" value="1" checked>
                                    <span class="form-check-label">1</span>
                                </label>
                                <label class="form-check form-check-inline">
                                    <input class="form-check-input" type="radio" name="corner-radius" value="1.5">
                                    <span class="form-check-label">1.5</span>
                                </label>
                                <label class="form-check form-check-inline">
                                    <input class="form-check-input" type="radio" name="corner-radius" value="2">
                                    <span class="form-check-label">2</span>
                                </label>
                            </div>
                        </div>
                    </div>
                    <div class="modal-footer">
                        <a href="#" class="btn" id="resetThemeChanges">重置更改</a>
                        <a href="#" class="btn btn-primary ms-auto" id="saveThemeChanges">保存</a>
                    </div>
                </div>
            </div>
        </div>
        <!-- 主内容区 -->
         <div class="page-wrapper">
             <!-- 页面内容 -->
             <div class="page-body">
                 <div class="container-xl">