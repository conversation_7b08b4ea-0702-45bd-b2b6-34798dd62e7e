<!-- 更新模板页面 -->
<div class="page-header d-print-none">
    <div class="container-xl">
        <div class="row g-2 align-items-center">
            <div class="col">
                <h2 class="page-title">
                    模板更新
                </h2>
                <div class="text-muted mt-1">检查并更新系统模板</div>
            </div>
            <!-- 页面操作按钮 -->
            <div class="col-auto ms-auto d-print-none">
                <div class="btn-list">
                    <a href="#" id="check-update" class="btn btn-primary d-inline-block">
                        <i class="ti ti-refresh"></i>
                        检查更新
                    </a>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- 更新内容 -->
<div class="page-body">
    <div class="container-xl">
        <div class="row row-deck row-cards">
            <div class="col-12">
                <div class="card">
                    <div class="card-header">
                        <h3 class="card-title">更新状态</h3>
                    </div>
                    <div class="card-body">
                        <div class="row">
                            <div class="col-12 col-md-6">
                                <div class="mb-3">
                                    <label class="form-label">当前版本</label>
                                    <div class="form-control-plaintext" id="current-version">正在获取版本信息...</div>
                                </div>
                            </div>
                            <div class="col-12 col-md-6">
                                <div class="mb-3">
                                    <label class="form-label">最新版本</label>
                                    <div class="form-control-plaintext" id="latest-version">等待检查...</div>
                                </div>
                            </div>
                        </div>
                        
                        <div id="update-available" class="d-none">
                            <div class="alert alert-success" role="alert">
                                <h4 class="alert-title">发现新版本</h4>
                                <div class="text-muted">有新版本可用，点击下方按钮开始更新</div>
                            </div>
                            
                            <div class="mt-3">
                                <button id="start-update" class="btn btn-primary w-100 w-md-auto">
                                    <i class="ti ti-download me-1"></i> 开始更新
                                </button>
                            </div>
                        </div>
                        
                        <div id="no-update" class="d-none">
                            <div class="alert alert-info" role="alert">
                                <h4 class="alert-title">已是最新版本</h4>
                                <div class="text-muted">您当前使用的已经是最新版本</div>
                            </div>
                        </div>
                        
                        <div id="update-progress" class="d-none">
                            <div class="mb-3">
                                <label class="form-label" id="progress-label">准备更新中...</label>
                                <div class="progress mb-2">
                                    <div class="progress-bar progress-bar-indeterminate bg-primary"></div>
                                </div>
                                <div class="text-muted text-wrap" id="progress-info">正在初始化更新过程，请耐心等待...</div>
                            </div>
                        </div>
                        
                        <div id="update-error" class="d-none">
                            <div class="alert alert-danger" role="alert">
                                <h4 class="alert-title">更新失败</h4>
                                <div class="text-muted" id="error-message">更新过程中发生错误</div>
                            </div>
                        </div>
                        
                        <div id="update-success" class="d-none">
                            <div class="alert alert-success" role="alert">
                                <h4 class="alert-title">更新成功</h4>
                                <div class="text-muted">模板已成功更新到最新版本</div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
            
            <div class="col-12 mt-3">
                <div class="card">
                    <div class="card-header">
                        <h3 class="card-title">更新日志</h3>
                    </div>
                    <div class="card-body">
                        <div id="update-log" class="markdown">
                            <p>暂无更新日志</p>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- 版本更新器 -->
<script src="version_updater.js"></script>

<!-- 更新脚本 -->
<script>
    // 获取当前版本和检查更新
    document.addEventListener('DOMContentLoaded', function() {
        // 获取当前版本
        fetch('get_version.php')
            .then(response => response.json())
            .then(data => {
                document.getElementById('current-version').textContent = data.version;
                // 自动检查更新
                checkUpdate();
            })
            .catch(error => {
                console.error('获取当前版本失败:', error);
                document.getElementById('current-version').textContent = '获取失败';
            });
        
        // 检查更新按钮点击事件
        document.getElementById('check-update').addEventListener('click', async function(e) {
            e.preventDefault();

            // 手动检查更新前，先强制更新版本文件
            if (window.versionUpdater) {
                await window.versionUpdater.forceUpdate();
            }

            // 然后执行检查更新
            checkUpdate();
        });
        
        // 开始更新按钮点击事件
        document.getElementById('start-update').addEventListener('click', startUpdate);
    });
    
    // 检查更新函数
    function checkUpdate() {
        // 隐藏之前的状态
        document.getElementById('update-available').classList.add('d-none');
        document.getElementById('no-update').classList.add('d-none');
        document.getElementById('update-error').classList.add('d-none');

        // 显示检查中状态
        document.getElementById('latest-version').textContent = '检查中...';

        // 请求检查更新
        fetch('update_handler.php', {
            method: 'POST',
            headers: {
                'Content-Type': 'application/x-www-form-urlencoded',
            },
            body: 'action=check_update'
        })
            .then(response => response.json())
            .then(data => {
                if (data.error) {
                    throw new Error(data.error);
                }

                // 显示最新版本
                document.getElementById('latest-version').textContent = data.version;

                // 更新当前版本显示（从服务器获取的实时版本）
                document.getElementById('current-version').textContent = data.current_version;

                // 检查是否有新版本
                if (data.has_update) {
                    // 有新版本
                    document.getElementById('update-available').classList.remove('d-none');
                    // 保存下载链接和更新信息
                    window.updateUrl = data.download_url;
                    window.updateContent = data.update_content;

                    // 显示更新内容
                    if (data.update_content) {
                        document.getElementById('update-log').innerHTML = '<p>' + data.update_content + '</p>';
                    }
                } else {
                    // 已是最新版本
                    document.getElementById('no-update').classList.remove('d-none');

                    // 显示服务器返回的消息或默认消息
                    const message = data.message || '当前已是最新版本，无更新内容';
                    document.getElementById('update-log').innerHTML = '<p>' + message + '</p>';
                }
            })
            .catch(error => {
                console.error('检查更新失败:', error);
                document.getElementById('latest-version').textContent = '检查失败';
                document.getElementById('update-error').classList.remove('d-none');
                document.getElementById('error-message').textContent = '检查更新失败: ' + error.message;
            });
    }
    
    // 开始更新函数
    function startUpdate() {
        // 隐藏更新按钮，显示进度条
        document.getElementById('update-available').classList.add('d-none');
        document.getElementById('update-progress').classList.remove('d-none');
        
        // 更新进度信息
        document.getElementById('progress-label').textContent = '正在获取下载链接...';
        document.getElementById('progress-info').textContent = '请稍候...';
        
        // 监听服务器发送事件
        let lastProgress = 0;
        
        // 使用 EventSource 处理服务器发送事件
        const eventSource = new EventSource('update_handler.php?action=start_update&url=' + encodeURIComponent(window.updateUrl));
        
        eventSource.onmessage = function(event) {
            try {
                const data = JSON.parse(event.data);
                
                if (data.progress !== undefined) {
                    // 更新进度条
                    const progressPercent = parseFloat(data.progress);
                    document.querySelector('.progress-bar').style.width = progressPercent + '%';
                    document.querySelector('.progress-bar').textContent = progressPercent.toFixed(2) + '%';
                    
                    // 更新进度信息
                    document.getElementById('progress-label').textContent = '正在下载文件...';
                    document.getElementById('progress-info').textContent = 
                        '已下载: ' + data.downloaded + ' / ' + data.total + ' (' + data.speed + ')';
                }
                
                lastProgress = data.progress || lastProgress;
            } catch (e) {
                console.error('解析进度数据失败:', e);
            }
        };
        
        eventSource.onerror = function(event) {
            console.error('更新过程中发生错误:', event);
            eventSource.close();
            
            // 如果还没有完成，显示错误
            if (lastProgress < 100) {
                document.getElementById('update-progress').classList.add('d-none');
                document.getElementById('update-error').classList.remove('d-none');
                document.getElementById('error-message').textContent = '更新过程中发生错误: 连接中断或服务器错误';
            }
        };
        
        eventSource.addEventListener('complete', function(event) {
            eventSource.close();
            
            try {
                const data = JSON.parse(event.data);
                if (data.error) {
                    throw new Error(data.error);
                }
                
                // 更新成功
                document.getElementById('update-progress').classList.add('d-none');
                document.getElementById('update-success').classList.remove('d-none');
            } catch (e) {
                console.error('更新失败:', e);
                document.getElementById('update-progress').classList.add('d-none');
                document.getElementById('update-error').classList.remove('d-none');
                document.getElementById('error-message').textContent = '更新失败: ' + (data.error || e.message);
            }
        });
    }
</script>