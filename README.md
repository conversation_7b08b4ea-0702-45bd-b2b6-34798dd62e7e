# PHP + Tabler 后台管理系统

这是一个使用PHP和Tabler UI框架构建的简单后台管理系统，不使用任何PHP框架和伪静态。

## 项目结构

```
/ewy
├── index.php          # 入口文件
├── header.php         # 公共头部
├── dashboard.php      # 仪表盘页面
└── README.md          # 项目说明文件
```

## 功能特点

- 使用纯PHP开发，无需框架
- 基于Tabler UI框架，美观现代化的界面
- 响应式设计，适配各种设备
- 简单的页面路由系统

## 使用方法

1. 访问 `index.php` 进入系统
2. 通过URL参数 `page` 切换不同页面，例如：`index.php?page=dashboard`

## 技术栈

- PHP 7.0+
- Tabler UI 1.0.0-beta17
- ApexCharts (用于图表展示)

## 注意事项

- 本项目仅用于测试，未连接数据库
- 所有数据均为静态展示