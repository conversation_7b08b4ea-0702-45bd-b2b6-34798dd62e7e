</div>
            </div>
        </div>
    </div>
    <!-- jQuery -->
    <script src="https://cdn.jsdmirror.com/npm/jquery@3.6.0/dist/jquery.min.js"></script>
    <!-- Tabler Core 最新版本 -->
    <script src="https://cdn.jsdmirror.com/npm/@tabler/core@latest/dist/js/tabler.min.js"></script>
    <!-- ApexCharts -->
    <script src="https://cdn.jsdmirror.com/npm/apexcharts@latest/dist/apexcharts.min.js"></script>
    <!-- 初始化脚本 -->
    <script>
        document.addEventListener("DOMContentLoaded", function() {
            // 激活当前页面的导航项
            const currentPage = new URLSearchParams(window.location.search).get('page') || 'dashboard';
            document.querySelectorAll('.navbar-nav .nav-link').forEach(link => {
                const href = link.getAttribute('href');
                if (href && href.includes(`page=${currentPage}`)) {
                    link.classList.add('active');
                }
            });
            
            // 初始化主题设置表单
            initThemeSettings();
            
            // 保存主题设置
            document.getElementById('saveThemeChanges').addEventListener('click', function(e) {
                e.preventDefault();
                saveThemeSettings();
            });
            
            // 重置主题设置
            document.getElementById('resetThemeChanges').addEventListener('click', function(e) {
                e.preventDefault();
                resetThemeSettings();
            });
        });
        
        // 初始化主题设置表单
        function initThemeSettings() {
            // 设置颜色模式
            const currentTheme = document.documentElement.getAttribute('data-bs-theme');
            document.querySelector(`input[name="color-mode"][value="${currentTheme}"]`).checked = true;
            
            // 设置颜色方案
            const currentColorScheme = getComputedStyle(document.documentElement).getPropertyValue('--tblr-primary').trim();
            const colorSchemeInput = document.querySelector(`input[name="color-scheme"][value="${currentColorScheme}"]`);
            if (colorSchemeInput) {
                colorSchemeInput.checked = true;
            }
            
            // 设置字体系列
            const fontFamily = getCookie('font_family') || 'sans-serif';
            document.querySelector(`input[name="font-family"][value="${fontFamily}"]`).checked = true;
            
            // 设置主题基础
            const themeBase = getCookie('theme_base') || 'gray';
            document.querySelector(`input[name="theme-base"][value="${themeBase}"]`).checked = true;
            
            // 设置圆角半径倍率
            const cornerRadius = getComputedStyle(document.documentElement).getPropertyValue('--tblr-border-radius-scale').trim() || '1';
            document.querySelector(`input[name="corner-radius"][value="${cornerRadius}"]`).checked = true;
        }
        
        // 保存主题设置
        function saveThemeSettings() {
            // 获取颜色模式
            const colorMode = document.querySelector('input[name="color-mode"]:checked').value;
            setCookie('theme_mode', colorMode, 30);
            document.documentElement.setAttribute('data-bs-theme', colorMode);
            
            // 获取颜色方案
            const colorScheme = document.querySelector('input[name="color-scheme"]:checked').value;
            setCookie('color_scheme', colorScheme, 30);
            document.documentElement.style.setProperty('--tblr-primary', colorScheme);
            
            // 获取字体系列
            const fontFamily = document.querySelector('input[name="font-family"]:checked').value;
            setCookie('font_family', fontFamily, 30);
            document.documentElement.style.setProperty('--tblr-font-sans-serif', `${fontFamily}, -apple-system, BlinkMacSystemFont, San Francisco, Segoe UI, Roboto, Helvetica Neue, sans-serif`);
            
            // 获取主题基础
            const themeBase = document.querySelector('input[name="theme-base"]:checked').value;
            setCookie('theme_base', themeBase, 30);
            
            // 设置圆角半径倍率
            const cornerRadius = document.querySelector('input[name="corner-radius"]:checked').value;
            setCookie('corner_radius', cornerRadius, 30);
            document.documentElement.style.setProperty('--tblr-border-radius-scale', cornerRadius);
            
            // 关闭模态框
            $('#themeSettingsModal').modal('hide');
        }
        
        // 重置主题设置
        function resetThemeSettings() {
            // 重置颜色模式
            document.querySelector('input[name="color-mode"][value="light"]').checked = true;
            
            // 重置颜色方案
            document.querySelector('input[name="color-scheme"][value="#206bc4"]').checked = true;
            
            // 重置字体系列
            document.querySelector('input[name="font-family"][value="sans-serif"]').checked = true;
            
            // 重置主题基础
            document.querySelector('input[name="theme-base"][value="gray"]').checked = true;
            
            // 重置圆角半径倍率
            document.querySelector('input[name="corner-radius"][value="1"]').checked = true;
            document.documentElement.style.setProperty('--tblr-border-radius-scale', '1');
        }
        
        // 设置Cookie
        function setCookie(name, value, days) {
            const date = new Date();
            date.setTime(date.getTime() + (days * 24 * 60 * 60 * 1000));
            const expires = "expires=" + date.toUTCString();
            document.cookie = name + "=" + value + ";" + expires + ";path=/";
        }
        
        // 获取Cookie
        function getCookie(name) {
            const cname = name + "=";
            const decodedCookie = decodeURIComponent(document.cookie);
            const ca = decodedCookie.split(';');
            for(let i = 0; i < ca.length; i++) {
                let c = ca[i];
                while (c.charAt(0) == ' ') {
                    c = c.substring(1);
                }
                if (c.indexOf(cname) == 0) {
                    return c.substring(cname.length, c.length);
                }
            }
            return "";
        }
    </script>
</body>
</html>