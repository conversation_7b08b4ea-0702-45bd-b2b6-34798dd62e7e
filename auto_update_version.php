<?php
/**
 * 自动更新版本文件脚本
 * 每分钟执行一次，从远程服务器下载最新的版本信息
 */

// 设置错误报告
error_reporting(E_ALL);
ini_set('display_errors', 0); // 生产环境关闭错误显示

// 包含更新处理函数
require_once 'update_handler.php';

// 记录日志函数
function writeLog($message) {
    $logFile = './temp/auto_update.log';
    $timestamp = date('Y-m-d H:i:s');
    $logMessage = "[$timestamp] $message\n";
    file_put_contents($logFile, $logMessage, FILE_APPEND | LOCK_EX);
}

// 主执行逻辑
try {
    writeLog('开始自动版本检查');

    // 固定从官方URL获取版本信息
    $versionFilePath = './temp/new.txt';
    $updateUrl = 'https://d.ly-y.cn/up/new.txt';
    writeLog('使用固定官方URL检查版本: ' . $updateUrl);

    // 从官方URL下载版本信息
    $result = downloadVersionFileWithDetailsFromUrl($updateUrl);

    if ($result['success']) {
        writeLog('版本信息获取成功，使用方法: ' . ($result['method'] ?? '未知'));

        // 检查是否有新版本
        if (file_exists($versionFilePath)) {
            $versionInfo = parseVersionFile($versionFilePath);
            if ($versionInfo) {
                // 获取本地config.php中的版本
                require_once 'config.php';
                $localVersion = $current_version;

                // 比较服务器New_version与本地config版本
                $hasUpdate = (version_compare($versionInfo['New_version'], $localVersion, '>'));

                if ($hasUpdate) {
                    writeLog('发现新版本: ' . $versionInfo['New_version'] . ' (本地版本: ' . $localVersion . ')');
                    writeLog('下载链接: ' . ($versionInfo['Download'] ?? '未知'));
                    writeLog('更新内容: ' . ($versionInfo['Update_content'] ?? '无'));
                } else {
                    writeLog('当前已是最新版本: ' . $localVersion . ' (服务器版本: ' . $versionInfo['New_version'] . ')');
                }
            } else {
                writeLog('版本文件解析失败');
            }
        } else {
            writeLog('版本文件不存在');
        }
    } else {
        writeLog('版本信息获取失败: ' . $result['error']);
    }

} catch (Exception $e) {
    writeLog('自动版本检查出错: ' . $e->getMessage());
}

writeLog('自动更新检查结束');
?>
