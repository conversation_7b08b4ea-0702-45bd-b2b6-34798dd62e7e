#!/usr/bin/env php
<?php
/**
 * Cron任务脚本 - 定期更新版本文件
 * 建议添加到系统cron任务中，每分钟执行一次：
 * * * * * * /usr/bin/php /path/to/your/project/cron_update.php
 */

// 设置脚本执行时间限制
set_time_limit(60);

// 设置错误报告
error_reporting(E_ALL);
ini_set('display_errors', 0);

// 切换到脚本所在目录
chdir(dirname(__FILE__));

// 包含更新处理函数
require_once 'update_handler.php';

// 记录日志函数
function cronLog($message) {
    $logFile = './temp/cron.log';
    $timestamp = date('Y-m-d H:i:s');
    $logMessage = "[$timestamp] $message\n";
    
    // 确保temp目录存在
    if (!is_dir('./temp')) {
        mkdir('./temp', 0755, true);
    }
    
    file_put_contents($logFile, $logMessage, FILE_APPEND | LOCK_EX);
}

// 主执行逻辑
try {
    cronLog('Cron任务开始执行');
    
    // 执行自动更新版本文件
    $result = autoUpdateVersionFile();
    
    if ($result) {
        cronLog('版本文件更新检查完成');
    } else {
        cronLog('版本文件更新失败');
    }
    
} catch (Exception $e) {
    cronLog('Cron任务执行出错: ' . $e->getMessage());
} catch (Error $e) {
    cronLog('Cron任务执行致命错误: ' . $e->getMessage());
}

cronLog('Cron任务执行结束');
?>
