/**
 * 版本更新器 - 自动更新版本文件
 */

class VersionUpdater {
    constructor() {
        this.updateInterval = 60000; // 1分钟 = 60000毫秒
        this.isUpdating = false;
        this.lastUpdateTime = 0;
        
        // 页面加载时立即检查一次
        this.init();
    }
    
    /**
     * 初始化
     */
    init() {
        // 立即执行一次更新检查
        this.updateVersionFile();
        
        // 设置定时器，每分钟执行一次
        setInterval(() => {
            this.updateVersionFile();
        }, this.updateInterval);
        
        console.log('版本更新器已启动，每分钟自动检查版本文件');
    }
    
    /**
     * 更新版本文件
     */
    async updateVersionFile() {
        // 避免重复执行
        if (this.isUpdating) {
            return;
        }
        
        // 检查距离上次更新是否超过1分钟
        const now = Date.now();
        if (now - this.lastUpdateTime < 60000) {
            return;
        }
        
        this.isUpdating = true;
        
        try {
            const response = await fetch('auto_update_version.php', {
                method: 'GET',
                cache: 'no-cache'
            });
            
            if (response.ok) {
                this.lastUpdateTime = now;
                console.log('版本文件更新检查完成');
            } else {
                console.warn('版本文件更新检查失败:', response.status);
            }
        } catch (error) {
            console.error('版本文件更新出错:', error);
        } finally {
            this.isUpdating = false;
        }
    }
    
    /**
     * 手动触发更新检查
     */
    async forceUpdate() {
        this.lastUpdateTime = 0; // 重置时间，强制更新
        await this.updateVersionFile();
    }
}

// 创建全局实例
window.versionUpdater = new VersionUpdater();
