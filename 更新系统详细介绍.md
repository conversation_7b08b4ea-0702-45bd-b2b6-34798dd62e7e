# EWY 项目更新系统详细介绍

## 概述

EWY 项目实现了一套完整的自动更新系统，支持版本检查、自动下载、实时进度显示和版本管理。该系统采用前后端分离的架构，通过多种方式确保更新的可靠性和用户体验。

## 系统架构

### 核心组件

1. **前端更新界面** (`update.php`)
2. **版本更新器** (`version_updater.js`)
3. **更新处理器** (`update_handler.php`)
4. **自动更新脚本** (`auto_update_version.php`)
5. **定时任务脚本** (`cron_update.php`)
6. **版本配置文件** (`config.php`)
7. **版本获取接口** (`get_version.php`)

### 数据流向

```
用户界面 → 版本检查 → 服务器版本文件 → 本地版本比较 → 更新决策 → 下载更新 → 版本更新
```

## 详细功能分析

### 1. 前端更新界面 (`update.php`)

#### 界面结构
- **页面头部**: 包含页面标题和检查更新按钮
- **更新状态卡片**: 显示当前版本和最新版本信息
- **状态显示区域**: 
  - 发现新版本提示
  - 已是最新版本提示
  - 更新进度条
  - 错误信息显示
  - 更新成功提示
- **更新日志卡片**: 显示版本更新内容

#### 核心JavaScript功能

**页面初始化流程**:
```javascript
document.addEventListener('DOMContentLoaded', function() {
    // 1. 获取当前版本信息
    fetch('get_version.php')
    // 2. 自动执行版本检查
    checkUpdate();
});
```

**版本检查逻辑**:
```javascript
function checkUpdate() {
    // 1. 重置界面状态
    // 2. 发送检查更新请求到 update_handler.php
    // 3. 解析返回的版本信息
    // 4. 根据 has_update 字段决定显示内容
}
```

**更新执行流程**:
```javascript
function startUpdate() {
    // 1. 使用 EventSource 建立 SSE 连接
    // 2. 实时接收下载进度数据
    // 3. 更新进度条和状态信息
    // 4. 处理完成或错误事件
}
```

### 2. 版本更新器 (`version_updater.js`)

#### 设计模式
采用单例模式，确保全局只有一个版本更新器实例。

#### 核心功能

**自动更新机制**:
```javascript
class VersionUpdater {
    constructor() {
        this.updateInterval = 60000; // 1分钟间隔
        this.isUpdating = false;     // 防重复执行
        this.lastUpdateTime = 0;     // 上次更新时间
    }
}
```

**更新策略**:
1. **时间控制**: 距离上次更新必须超过1分钟
2. **状态锁定**: 使用 `isUpdating` 标志防止并发执行
3. **定时触发**: 每分钟自动检查一次
4. **手动触发**: 支持强制立即更新

### 3. 更新处理器 (`update_handler.php`)

#### 主要功能模块

**1. 版本检查 (`checkUpdate`)**
```php
function checkUpdate() {
    // 1. 创建临时目录
    // 2. 从官方URL下载版本文件
    // 3. 解析版本信息
    // 4. 与本地版本比较
    // 5. 返回JSON格式的检查结果
}
```

**2. 更新执行 (`startUpdate`)**
- 使用 **Server-Sent Events (SSE)** 技术
- 实时推送下载进度
- 支持断点续传
- 自动解压和文件替换

**3. 多种下载方式**
系统支持多种下载方式，按优先级尝试：
1. **cURL**: 功能最全面，支持进度回调
2. **wget**: 命令行工具，显示详细进度
3. **file_get_contents**: PHP内置函数
4. **fopen**: 流式下载

#### 版本文件解析

版本文件格式示例：
```
Version_push->1.0.0-2.7.1
Download->https://example.com/update.zip
New_version=2.7.2
Update_content=修复了若干bug，增加了新功能
```

解析逻辑：
```php
function parseVersionFile($filePath) {
    // 逐行解析版本文件
    // 支持多种格式的键值对
    // 返回结构化的版本信息数组
}
```

### 4. 自动更新脚本 (`auto_update_version.php`)

#### 功能特点
- **定期执行**: 每分钟自动检查版本
- **日志记录**: 详细记录更新过程
- **错误处理**: 完善的异常捕获机制

#### 执行流程
```php
// 1. 记录开始日志
writeLog('开始自动版本检查');

// 2. 从官方URL下载版本信息
$result = downloadVersionFileWithDetailsFromUrl($updateUrl);

// 3. 解析版本信息并比较
if ($hasUpdate) {
    writeLog('发现新版本: ' . $newVersion);
} else {
    writeLog('当前已是最新版本');
}
```

### 5. 定时任务脚本 (`cron_update.php`)

#### 设计目的
为系统级定时任务提供入口点，可以通过 crontab 定期执行。

#### 推荐配置
```bash
# 每分钟执行一次版本检查
* * * * * /usr/bin/php /path/to/project/cron_update.php
```

#### 安全特性
- **执行时间限制**: 最多执行60秒
- **目录切换**: 自动切换到脚本所在目录
- **错误处理**: 捕获所有类型的错误和异常

### 6. 版本配置管理 (`config.php`)

#### 核心功能

**版本存储**:
```php
$current_version = '2.7.1';
```

**版本更新函数**:
```php
function updateLocalVersion($newVersion) {
    // 使用正则表达式替换版本号
    $pattern = '/(\$current_version\s*=\s*[\'"])([^\'"]*)([\'"];)/';
    $replacement = '${1}' . $newVersion . '${3}';
    // 写入文件
}
```

**版本比较**:
```php
function compareVersions($version1, $version2) {
    return version_compare($version1, $version2);
}
```

## 更新流程详解

### 完整更新流程

1. **版本检查阶段**
   - 前端定时器触发版本文件更新
   - 从官方服务器下载最新版本信息
   - 解析版本文件获取版本号和下载链接
   - 与本地版本进行比较

2. **用户交互阶段**
   - 用户访问更新页面
   - 自动显示版本检查结果
   - 如有新版本，显示更新按钮和更新内容

3. **下载更新阶段**
   - 用户点击开始更新
   - 建立SSE连接获取实时进度
   - 多方式尝试下载更新包
   - 实时显示下载进度和速度

4. **安装更新阶段**
   - 验证下载文件完整性
   - 备份当前文件
   - 解压更新包
   - 替换项目文件
   - 更新本地版本号

5. **完成阶段**
   - 清理临时文件
   - 显示更新成功信息
   - 记录更新日志

### 错误处理机制

1. **网络错误**: 多种下载方式备选
2. **文件错误**: 完整性验证和备份恢复
3. **权限错误**: 详细的错误信息提示
4. **并发控制**: 锁文件机制防止重复执行

## 技术特点

### 1. 实时进度显示
使用 **Server-Sent Events (SSE)** 技术实现：
- 无需轮询，服务器主动推送
- 实时显示下载进度、速度、已下载大小
- 支持长时间连接，适合大文件下载

### 2. 多重下载保障
- **主要方式**: cURL (支持进度回调)
- **备选方式**: wget (命令行进度显示)
- **兜底方式**: file_get_contents, fopen
- **断点续传**: 支持网络中断后继续下载

### 3. 版本管理策略
- **语义化版本**: 使用 `version_compare` 进行版本比较
- **自动更新**: 更新完成后自动修改配置文件中的版本号
- **版本推送**: 支持版本推送范围控制

### 4. 安全性考虑
- **文件验证**: 下载完成后验证文件完整性
- **备份机制**: 更新前自动备份重要文件
- **权限检查**: 检查文件写入权限
- **锁文件**: 防止并发更新导致的冲突

## 配置说明

### 关键配置项

1. **更新服务器URL**: `https://d.ly-y.cn/up/new.txt`
2. **检查间隔**: 60秒 (1分钟)
3. **临时目录**: `./temp/`
4. **锁文件**: `./temp/update.lock`
5. **日志文件**: `./temp/auto_update.log`, `./temp/cron.log`

### 环境要求

- **PHP版本**: 7.0+
- **必需扩展**: cURL, ZIP
- **文件权限**: 项目目录可写
- **网络访问**: 能够访问更新服务器

## 使用建议

### 1. 部署建议
- 确保 `temp` 目录有写入权限
- 配置定时任务定期检查版本
- 监控日志文件了解更新状态

### 2. 维护建议
- 定期清理临时文件和日志
- 监控更新服务器的可用性
- 备份重要配置文件

### 3. 扩展建议
- 可以添加更新通知功能
- 支持回滚到之前版本
- 增加更新前的数据备份功能

## 总结

EWY 项目的更新系统是一个功能完整、设计合理的自动更新解决方案。它通过多层次的架构设计、完善的错误处理机制和用户友好的界面，为用户提供了可靠的更新体验。系统的模块化设计使得各个组件职责清晰，便于维护和扩展。
