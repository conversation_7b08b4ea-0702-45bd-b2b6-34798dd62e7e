<?php
// 配置文件

// 当前本地安装的版本（更新完成后会自动修改此版本号）
$current_version = '2.7.1';

/**
 * 更新本地版本号
 * @param string $newVersion 新版本号
 * @return bool 是否成功
 */
function updateLocalVersion($newVersion) {
    $configFile = __FILE__;
    $content = file_get_contents($configFile);

    if ($content === false) {
        return false;
    }

    // 使用正则表达式替换版本号
    $pattern = '/(\$current_version\s*=\s*[\'"])([^\'"]*)([\'"];)/';
    $replacement = '${1}' . $newVersion . '${3}';
    $newContent = preg_replace($pattern, $replacement, $content);

    if ($newContent === null || $newContent === $content) {
        return false;
    }

    return file_put_contents($configFile, $newContent) !== false;
}

/**
 * 版本比较函数
 * @param string $version1 版本1
 * @param string $version2 版本2
 * @return int -1: version1 < version2, 0: 相等, 1: version1 > version2
 */
function compareVersions($version1, $version2) {
    return version_compare($version1, $version2);
}

// 其他配置项可以在此添加
?>